using System;
using System.Numerics;
using ImGuiNET;

namespace Pictomancy
{
    public class PctDrawList : IDisposable
    {
        private bool _disposed = false;

        public void AddCircle(Vector3 worldPosition, float radius, uint color, float thickness = 1.0f)
        {
            if (_disposed) return;
            
            // Convert world position to screen coordinates
            if (TryWorldToScreen(worldPosition, out Vector2 screenPos))
            {
                var drawList = ImGui.GetBackgroundDrawList();
                drawList.AddCircle(screenPos, radius, color, 0, thickness);
            }
        }

        public void AddCircleFilled(Vector3 worldPosition, float radius, uint color)
        {
            if (_disposed) return;
            
            if (TryWorldToScreen(worldPosition, out Vector2 screenPos))
            {
                var drawList = ImGui.GetBackgroundDrawList();
                drawList.AddCircleFilled(screenPos, radius, color);
            }
        }

        public void AddLine(Vector3 worldStart, Vector3 worldEnd, uint color, float thickness = 1.0f)
        {
            if (_disposed) return;
            
            if (TryWorldToScreen(worldStart, out Vector2 screenStart) && 
                TryWorldToScreen(worldEnd, out Vector2 screenEnd))
            {
                var drawList = ImGui.GetBackgroundDrawList();
                drawList.AddLine(screenStart, screenEnd, color, thickness);
            }
        }

        public void AddText(Vector3 worldPosition, string text, uint color, float fontSize = 12.0f)
        {
            if (_disposed) return;
            
            if (TryWorldToScreen(worldPosition, out Vector2 screenPos))
            {
                var drawList = ImGui.GetBackgroundDrawList();
                drawList.AddText(screenPos, color, text);
            }
        }

        public void AddRect(Vector3 worldPosition, Vector2 size, uint color, float thickness = 1.0f)
        {
            if (_disposed) return;
            
            if (TryWorldToScreen(worldPosition, out Vector2 screenPos))
            {
                var drawList = ImGui.GetBackgroundDrawList();
                drawList.AddRect(screenPos, screenPos + size, color, 0.0f, ImDrawFlags.None, thickness);
            }
        }

        public void AddRectFilled(Vector3 worldPosition, Vector2 size, uint color)
        {
            if (_disposed) return;
            
            if (TryWorldToScreen(worldPosition, out Vector2 screenPos))
            {
                var drawList = ImGui.GetBackgroundDrawList();
                drawList.AddRectFilled(screenPos, screenPos + size, color);
            }
        }

        private bool TryWorldToScreen(Vector3 worldPos, out Vector2 screenPos)
        {
            screenPos = Vector2.Zero;
            
            try
            {
                // This is a simplified world-to-screen conversion
                // In a real implementation, you'd use the game's camera matrices
                // For now, we'll use a basic projection
                
                // Get camera position (simplified - you'd get this from the game)
                var cameraPos = Vector3.Zero; // Replace with actual camera position
                var relative = worldPos - cameraPos;
                
                // Simple perspective projection
                if (relative.Z <= 0) return false; // Behind camera
                
                var screenWidth = ImGui.GetIO().DisplaySize.X;
                var screenHeight = ImGui.GetIO().DisplaySize.Y;
                
                screenPos.X = (relative.X / relative.Z) * 1000 + screenWidth / 2;
                screenPos.Y = (relative.Y / relative.Z) * 1000 + screenHeight / 2;
                
                return screenPos.X >= 0 && screenPos.X <= screenWidth && 
                       screenPos.Y >= 0 && screenPos.Y <= screenHeight;
            }
            catch
            {
                return false;
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _disposed = true;
            }
        }
    }
}
