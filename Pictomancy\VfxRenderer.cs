using System;
using System.Collections.Generic;
using System.Numerics;
using Dalamud.Game.ClientState.Objects.Types;
using Dalamud.Logging;

namespace Pictomancy
{
    public class VfxRenderer : IDisposable
    {
        private readonly Dictionary<string, VfxInstance> _activeVfx = new();
        private bool _disposed = false;

        public void AddOmen(string id, string vfxPath, Vector3 position, float radius, uint color = 0xFF0000FF)
        {
            if (_disposed) return;
            
            try
            {
                var vfx = new VfxInstance
                {
                    Id = id,
                    VfxPath = vfxPath,
                    Position = position,
                    Radius = radius,
                    Color = color,
                    Type = VfxType.Omen
                };
                
                _activeVfx[id] = vfx;
                PluginLog.Debug($"Added omen VFX: {id} at {position}");
            }
            catch (Exception ex)
            {
                PluginLog.Error($"Error adding omen VFX: {ex.Message}");
            }
        }

        public void AddCircle(string id, Vector3 position, float radius, uint color = 0xFF0000FF)
        {
            AddOmen(id, "general01bf", position, radius, color);
        }

        public void AddLockon(string id, string vfxPath, IGameObject target)
        {
            if (_disposed || target == null) return;
            
            try
            {
                var vfx = new VfxInstance
                {
                    Id = id,
                    VfxPath = vfxPath,
                    Position = target.Position,
                    Target = target,
                    Type = VfxType.Lockon
                };
                
                _activeVfx[id] = vfx;
                PluginLog.Debug($"Added lockon VFX: {id} on {target.Name}");
            }
            catch (Exception ex)
            {
                PluginLog.Error($"Error adding lockon VFX: {ex.Message}");
            }
        }

        public void AddChanneling(string id, string vfxPath, IGameObject source, IGameObject target)
        {
            if (_disposed || source == null || target == null) return;
            
            try
            {
                var vfx = new VfxInstance
                {
                    Id = id,
                    VfxPath = vfxPath,
                    Position = source.Position,
                    Target = target,
                    Source = source,
                    Type = VfxType.Channeling
                };
                
                _activeVfx[id] = vfx;
                PluginLog.Debug($"Added channeling VFX: {id} from {source.Name} to {target.Name}");
            }
            catch (Exception ex)
            {
                PluginLog.Error($"Error adding channeling VFX: {ex.Message}");
            }
        }

        public void RemoveVfx(string id)
        {
            if (_disposed) return;
            
            if (_activeVfx.Remove(id))
            {
                PluginLog.Debug($"Removed VFX: {id}");
            }
        }

        public void ClearAll()
        {
            if (_disposed) return;
            
            _activeVfx.Clear();
            PluginLog.Debug("Cleared all VFX");
        }

        public void Update()
        {
            if (_disposed) return;
            
            // Update VFX positions for moving targets
            var toRemove = new List<string>();
            
            foreach (var kvp in _activeVfx)
            {
                var vfx = kvp.Value;
                
                // Update position if target exists and is valid
                if (vfx.Target != null)
                {
                    if (vfx.Target.IsValid())
                    {
                        vfx.Position = vfx.Target.Position;
                    }
                    else
                    {
                        toRemove.Add(kvp.Key);
                    }
                }
            }
            
            // Remove invalid VFX
            foreach (var id in toRemove)
            {
                RemoveVfx(id);
            }
        }

        public IReadOnlyDictionary<string, VfxInstance> GetActiveVfx()
        {
            return _activeVfx;
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                ClearAll();
                _disposed = true;
            }
        }
    }

    public class VfxInstance
    {
        public string Id { get; set; } = string.Empty;
        public string VfxPath { get; set; } = string.Empty;
        public Vector3 Position { get; set; }
        public float Radius { get; set; }
        public uint Color { get; set; }
        public VfxType Type { get; set; }
        public IGameObject? Target { get; set; }
        public IGameObject? Source { get; set; }
    }

    public enum VfxType
    {
        Omen,
        Lockon,
        Channeling,
        Marker
    }
}
