using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using Dalamud.Game.ClientState.Conditions;
using Dalamud.Game.ClientState.Objects.SubKinds;
using ImGuiNET;
using PvPLinePlugin.Utils;
using Pictomancy;

namespace PvPLinePlugin;

public readonly struct PlayerDisplayData(PlayerRole role, float healthPercentage, bool isAlly,
    bool isLowHealth, bool isVulnerable, bool isCrowdControlled, bool hasDefensiveBuff, bool isGuarding, bool isRecuperating)
{
    public PlayerRole Role { get; } = role;
    public float HealthPercentage { get; } = healthPercentage;
    public bool IsAlly { get; } = isAlly;
    public bool IsLowHealth { get; } = isLowHealth;
    public bool IsVulnerable { get; } = isVulnerable;
    public bool IsCrowdControlled { get; } = isCrowdControlled;
    public bool HasDefensiveBuff { get; } = hasDefensiveBuff;
    public bool IsGuarding { get; } = isGuarding;
    public bool IsRecuperating { get; } = isRecuperating;
}



public sealed class PvPOverlay : IDisposable
{
    private readonly Configuration config;

    // Cache for PvP territory IDs to avoid repeated switch statements
    private static readonly HashSet<uint> PvPTerritoryIds = new()
    {
        // Frontlines
        376, // Seal Rock
        554, // The Fields of Glory (Shatter)
        692, // The Borderland Ruins (Secure)

        // Rival Wings
        691, // Astragalos
        789, // Hidden Gorge

        // Crystalline Conflict
        1002, 1007, 1008, 1009, 1155, 1156, 1157, 1158,

        // The Wolves' Den
        250 // The Wolves' Den Pier
    };

    // Object pooling for better memory management
    private readonly Queue<List<IPlayerCharacter>> _listPool = new();

    // Performance tracking
    private int _frameCount = 0;
    private DateTime _lastPerformanceLog = DateTime.MinValue;

    public PvPOverlay(Plugin plugin)
    {
        config = plugin.Configuration;
        Plugin.PluginInterface.UiBuilder.Draw += DrawOverlay;
    }

    public void Dispose()
    {
        Plugin.PluginInterface.UiBuilder.Draw -= DrawOverlay;

        // Clean up pools
        while (_listPool.Count > 0)
        {
            _listPool.Dequeue().Clear();
        }

        GC.SuppressFinalize(this);
    }

    private void DrawOverlay()
    {
        try
        {
            _frameCount++;

            if (!ShouldDrawLines()) return;

            var localPlayer = Plugin.ClientState.LocalPlayer;
            if (localPlayer == null) return;

            var (enemies, allies) = GetPlayersAroundMe(localPlayer);

            DrawPlayerLines(localPlayer, enemies, false);
            DrawPlayerLines(localPlayer, allies, true);



            // Performance logging (optional, can be disabled in release)
            LogPerformanceIfNeeded();
        }
        catch (Exception ex)
        {
            // Log error but don't crash the game
            Plugin.Log.Error($"Error in PvP overlay drawing: {ex.Message}");
        }
    }

    private void LogPerformanceIfNeeded()
    {
        var now = DateTime.UtcNow;
        if ((now - _lastPerformanceLog).TotalSeconds >= 30) // Log every 30 seconds
        {
            var fps = _frameCount / 30.0;
            if (fps < 30) // Only log if performance is concerning
            {
                Plugin.Log.Warning($"PvP Overlay FPS: {fps:F1} (Pool size: {_listPool.Count})");
            }
            _frameCount = 0;
            _lastPerformanceLog = now;
        }
    }

    private void DrawPlayerLines(IPlayerCharacter localPlayer, List<IPlayerCharacter> players, bool isAlly)
    {
        var shouldShow = isAlly ? config.ShowAllies : config.ShowEnemies;
        if (!shouldShow) return;

        foreach (var player in players)
        {
            if (player?.IsValid() == true)
            {
                DrawLineToPlayer(localPlayer, player, isAlly);
            }
        }
    }



    private bool ShouldDrawLines()
    {
        return config.Enabled &&
               Plugin.ClientState.LocalPlayer != null &&
               (!config.OnlyInPvP || IsPvPTerritory(Plugin.ClientState.TerritoryType)) &&
               (!config.ShowInCombatOnly || Plugin.Condition[ConditionFlag.InCombat]);
    }

    private (List<IPlayerCharacter> enemies, List<IPlayerCharacter> allies) GetPlayersAroundMe(IPlayerCharacter localPlayer)
    {
        // Use object pooling to reduce allocations
        var enemies = GetPooledList();
        var allies = GetPooledList();

        try
        {
            var maxDistanceSquared = config.MaxDistance * config.MaxDistance;
            var localPosition = localPlayer.Position;
            var localGameObjectId = localPlayer.GameObjectId;

            foreach (var obj in Plugin.ObjectTable)
            {
                if (obj is not IPlayerCharacter player ||
                    player.GameObjectId == localGameObjectId ||
                    !player.IsValid() ||
                    player.IsDead)
                    continue;

                // Early distance check to avoid expensive calculations
                var distanceSquared = Vector3.DistanceSquared(localPosition, player.Position);
                if (distanceSquared > maxDistanceSquared)
                    continue;

                if (IsAllyPlayer(player))
                    allies.Add(player);
                else if (IsEnemyPlayer(player))
                    enemies.Add(player);
            }

            // Create new lists to return (caller will dispose of pooled lists)
            return (new List<IPlayerCharacter>(enemies), new List<IPlayerCharacter>(allies));
        }
        finally
        {
            // Return lists to pool
            ReturnPooledList(enemies);
            ReturnPooledList(allies);
        }
    }

    private List<IPlayerCharacter> GetPooledList()
    {
        if (_listPool.Count > 0)
        {
            var list = _listPool.Dequeue();
            list.Clear();
            return list;
        }
        return new List<IPlayerCharacter>(10);
    }

    private void ReturnPooledList(List<IPlayerCharacter> list)
    {
        if (list.Capacity <= 50) // Don't pool overly large lists
        {
            list.Clear();
            _listPool.Enqueue(list);
        }
    }



    private static bool IsAllyPlayer(IPlayerCharacter player)
    {
        if (Plugin.PartyList == null) return false;

        foreach (var partyMember in Plugin.PartyList)
        {
            if (partyMember?.GameObject?.GameObjectId == player.GameObjectId)
                return true;
        }

        // TODO: Add alliance detection for large-scale PvP modes
        // This would require additional game state information
        return false;
    }

    private static bool IsEnemyPlayer(IPlayerCharacter player)
    {
        // If they're an ally, they're not an enemy
        // In PvP zones, assume all non-ally players are enemies
        return !IsAllyPlayer(player);
    }

    private static float GetHealthPercentage(IPlayerCharacter player)
    {
        if (player?.CurrentHp == null || player?.MaxHp == null || player.MaxHp == 0)
            return 100.0f;

        return (float)player.CurrentHp / player.MaxHp * 100.0f;
    }

    private static bool TryGetScreenPositions(IPlayerCharacter localPlayer, IPlayerCharacter otherPlayer,
        out Vector2 startPos, out Vector2 endPos)
    {
        startPos = endPos = Vector2.Zero;

        if (localPlayer?.Position == null || otherPlayer?.Position == null)
            return false;

        if (!Plugin.GameGui.WorldToScreen(localPlayer.Position, out var localScreenPos) ||
            !Plugin.GameGui.WorldToScreen(otherPlayer.Position, out var otherPlayerScreenPos))
            return false;

        if (float.IsNaN(localScreenPos.X) || float.IsNaN(localScreenPos.Y) ||
            float.IsNaN(otherPlayerScreenPos.X) || float.IsNaN(otherPlayerScreenPos.Y))
            return false;

        startPos = new Vector2(localScreenPos.X, localScreenPos.Y);
        endPos = new Vector2(otherPlayerScreenPos.X, otherPlayerScreenPos.Y);
        return true;
    }

    private (Vector4 color, float thickness) GetLineAppearance(PlayerDisplayData data)
    {

        if (data.IsAlly)
        {
            var color = config.DifferentColorsForAllies && config.ColorCodeByRole
                ? BlendColors(JobHelper.GetRoleColor(data.Role), config.AllyLineColor, 0.5f)
                : config.AllyLineColor;
            return (color, config.AllyLineThickness);
        }

        if (data.IsLowHealth)
        {
            var color = config.LowHealthLineColor;
            if (config.PulseKillableTargets)
            {
                var pulseIntensity = (float)(0.7f + 0.3f * Math.Sin(DateTime.Now.Millisecond * 0.01f));
                color = new Vector4(color.X, color.Y, color.Z, color.W * pulseIntensity);
            }
            return (color, config.LowHealthLineThickness);
        }

        if (data.IsVulnerable)
        {
            var vulnerableColor = new Vector4(1.0f, 0.5f, 0.0f, 1.0f); // Orange
            return (vulnerableColor, config.LineThickness + 1.0f);
        }

        // Defensive buff indicators
        if (data.IsGuarding)
        {
            var guardColor = new Vector4(0.0f, 0.0f, 1.0f, 1.0f); // Blue for Guard (avoid target)
            return (guardColor, config.LineThickness + 2.0f);
        }

        if (data.IsRecuperating)
        {
            var recuperateColor = new Vector4(0.0f, 0.8f, 0.8f, 1.0f); // Cyan for Recuperate (healing)
            return (recuperateColor, config.LineThickness + 1.0f);
        }

        if (data.HasDefensiveBuff)
        {
            var defensiveColor = new Vector4(0.5f, 0.5f, 1.0f, 1.0f); // Light blue for other defensive buffs
            return (defensiveColor, config.LineThickness + 0.5f);
        }

        var normalColor = config.ColorCodeByRole
            ? JobHelper.GetRoleColor(data.Role)
            : config.LineColor;
        return (normalColor, config.LineThickness);
    }

    private static Vector4 BlendColors(Vector4 color1, Vector4 color2, float ratio)
    {
        return new Vector4(
            color1.X * ratio + color2.X * (1 - ratio),
            color1.Y * ratio + color2.Y * (1 - ratio),
            color1.Z * ratio + color2.Z * (1 - ratio),
            color2.W
        );
    }

    private void DrawLineToPlayer(IPlayerCharacter localPlayer, IPlayerCharacter otherPlayer, bool isAlly)
    {
        // Check distance
        var distance = Vector3.Distance(localPlayer.Position, otherPlayer.Position);
        if (distance > config.MaxDistance) return;

        if (!TryGetScreenPositions(localPlayer, otherPlayer, out var startPos, out var endPos))
            return;

        var lineLength = Vector2.Distance(startPos, endPos);
        if (lineLength is < 5.0f or > 2000.0f) return;

        var playerData = GatherPlayerData(otherPlayer, isAlly);
        DrawPlayerLine(startPos, endPos, playerData);

        if (config.ShowDistance)
            DrawDistanceText(ImGui.GetBackgroundDrawList(), localPlayer, otherPlayer, startPos, endPos);

        DrawPlayerInfo(ImGui.GetBackgroundDrawList(), otherPlayer, endPos, playerData);
    }

    private PlayerDisplayData GatherPlayerData(IPlayerCharacter player, bool isAlly)
    {
        var role = JobHelper.GetPlayerRole(player);
        var healthPercentage = GetHealthPercentage(player);
        var isLowHealth = config.ShowLowHealthIndicator && healthPercentage <= config.LowHealthThreshold && !isAlly;
        var isVulnerable = !isAlly && StatusEffectHelper.IsVulnerable(player);
        var isCrowdControlled = StatusEffectHelper.IsCrowdControlled(player);
        var hasDefensiveBuff = StatusEffectHelper.HasDefensiveBuff(player);
        var isGuarding = StatusEffectHelper.IsGuarding(player);
        var isRecuperating = StatusEffectHelper.IsRecuperating(player);

        return new PlayerDisplayData(role, healthPercentage, isAlly, isLowHealth, isVulnerable, isCrowdControlled, hasDefensiveBuff, isGuarding, isRecuperating);
    }

    private void DrawPlayerLine(Vector2 startPos, Vector2 endPos, PlayerDisplayData data)
    {
        switch (config.IndicatorType)
        {
            case IndicatorType.Lines:
                DrawLineIndicator(startPos, endPos, data);
                break;
            case IndicatorType.Outlines:
                // Outline drawing would require more complex rendering
                DrawLineIndicator(startPos, endPos, data); // Fallback to lines for now
                break;
            case IndicatorType.Nameplates:
                // Enhanced nameplate at endPos
                DrawEnhancedNameplate(endPos, data);
                break;
            case IndicatorType.Icons:
                DrawIconIndicator(endPos, data);
                break;
            case IndicatorType.DirectionalArrows:
                DrawDirectionalArrow(startPos, endPos, data);
                break;
            case IndicatorType.HealthBars:
                DrawHealthBarIndicator(endPos, data);
                break;
            case IndicatorType.Combination:
                DrawLineIndicator(startPos, endPos, data);
                if (config.ShowDirectionalArrows)
                    DrawDirectionalArrow(startPos, endPos, data);
                break;
            // New enhanced indicator types
            case IndicatorType.WorldCircles:
                DrawWorldCircleIndicator(startPos, endPos, data);
                break;
            case IndicatorType.VfxOmens:
                DrawVfxOmenIndicator(startPos, endPos, data);
                break;
            case IndicatorType.VfxTethers:
                DrawVfxTetherIndicator(startPos, endPos, data);
                break;
            case IndicatorType.GroundMarkers:
                DrawGroundMarkerIndicator(startPos, endPos, data);
                break;
            case IndicatorType.FloatingIcons:
                DrawFloatingIconIndicator(startPos, endPos, data);
                break;
            case IndicatorType.ScreenEdgeArrows:
                DrawScreenEdgeArrowIndicator(startPos, endPos, data);
                break;
            case IndicatorType.GradientLines:
                DrawGradientLineIndicator(startPos, endPos, data);
                break;
            case IndicatorType.AnimatedPulse:
                DrawAnimatedPulseIndicator(startPos, endPos, data);
                break;
        }
    }

    private void DrawLineIndicator(Vector2 startPos, Vector2 endPos, PlayerDisplayData data)
    {
        var (lineColor, lineThickness) = GetLineAppearance(data);
        var color = ImGui.ColorConvertFloat4ToU32(lineColor);

        if (config.UseDashedLines)
        {
            ImGui.GetBackgroundDrawList().AddDashedLine(startPos, endPos, color, lineThickness, config.DashLength, config.GapLength);
        }
        else
        {
            ImGui.GetBackgroundDrawList().AddLine(startPos, endPos, color, lineThickness);
        }
    }

    private void DrawEnhancedNameplate(Vector2 screenPos, PlayerDisplayData data)
    {
        var (color, _) = GetLineAppearance(data);
        var drawList = ImGui.GetBackgroundDrawList();

        // Draw a colored rectangle as enhanced nameplate
        var size = new Vector2(80, 20);
        var rectMin = new Vector2(screenPos.X - size.X / 2, screenPos.Y - 30);
        var rectMax = new Vector2(rectMin.X + size.X, rectMin.Y + size.Y);

        var bgColor = new Vector4(0, 0, 0, 0.7f);
        drawList.AddRectFilled(rectMin, rectMax, ImGui.ColorConvertFloat4ToU32(bgColor));
        drawList.AddRect(rectMin, rectMax, ImGui.ColorConvertFloat4ToU32(color), 0, ImDrawFlags.None, 2);

        // Add role indicator
        var roleString = data.Role.ToString();
        var roleText = roleString.Length > 3 ? roleString[..3] : roleString;
        var textPos = new Vector2(rectMin.X + 5, rectMin.Y + 2);
        drawList.AddText(textPos, ImGui.ColorConvertFloat4ToU32(color), roleText);
    }

    private void DrawIconIndicator(Vector2 screenPos, PlayerDisplayData data)
    {
        var (color, _) = GetLineAppearance(data);
        var drawList = ImGui.GetBackgroundDrawList();

        // Draw a simple circle icon
        var radius = config.IconSize / 2;
        drawList.AddCircleFilled(screenPos, radius, ImGui.ColorConvertFloat4ToU32(color));
        drawList.AddCircle(screenPos, radius, ImGui.ColorConvertFloat4ToU32(Vector4.One), 0, 2);

        // Add role letter in center
        var roleChar = data.Role.ToString()[0].ToString();
        var textSize = ImGui.CalcTextSize(roleChar);
        var textPos = new Vector2(screenPos.X - textSize.X / 2, screenPos.Y - textSize.Y / 2);
        drawList.AddText(textPos, ImGui.ColorConvertFloat4ToU32(Vector4.One), roleChar);
    }

    private void DrawDirectionalArrow(Vector2 startPos, Vector2 endPos, PlayerDisplayData data)
    {
        var (color, _) = GetLineAppearance(data);
        var drawList = ImGui.GetBackgroundDrawList();

        // Calculate direction vector
        var direction = Vector2.Normalize(endPos - startPos);
        var arrowSize = config.ArrowSize;

        // Draw arrow at screen edge pointing toward enemy
        var screenSize = ImGui.GetIO().DisplaySize;
        var edgePos = GetScreenEdgePosition(startPos, direction, screenSize);

        DrawArrowAtPosition(drawList, edgePos, direction, arrowSize, color);
    }

    private void DrawHealthBarIndicator(Vector2 screenPos, PlayerDisplayData data)
    {
        var (color, _) = GetLineAppearance(data);
        var drawList = ImGui.GetBackgroundDrawList();

        // Draw health bar above enemy
        var barWidth = 60f;
        var barHeight = 8f;
        var barPos = new Vector2(screenPos.X - barWidth / 2, screenPos.Y - 40);

        // Background
        var bgColor = new Vector4(0, 0, 0, 0.7f);
        drawList.AddRectFilled(barPos, new Vector2(barPos.X + barWidth, barPos.Y + barHeight),
                              ImGui.ColorConvertFloat4ToU32(bgColor));

        // Health fill
        var healthWidth = barWidth * (data.HealthPercentage / 100f);
        var healthColor = data.HealthPercentage < 30 ? new Vector4(1, 0, 0, 1) : color;
        drawList.AddRectFilled(barPos, new Vector2(barPos.X + healthWidth, barPos.Y + barHeight),
                              ImGui.ColorConvertFloat4ToU32(healthColor));

        // Border
        drawList.AddRect(barPos, new Vector2(barPos.X + barWidth, barPos.Y + barHeight),
                        ImGui.ColorConvertFloat4ToU32(Vector4.One), 0, ImDrawFlags.None, 1);
    }

    private static Vector2 GetScreenEdgePosition(Vector2 center, Vector2 direction, Vector2 screenSize)
    {
        var margin = 50f;
        var x = direction.X > 0 ? screenSize.X - margin : margin;
        var y = direction.Y > 0 ? screenSize.Y - margin : margin;

        // Choose the edge that the direction is pointing toward
        if (Math.Abs(direction.X) > Math.Abs(direction.Y))
            return new Vector2(x, center.Y);
        else
            return new Vector2(center.X, y);
    }

    private static void DrawArrowAtPosition(ImDrawListPtr drawList, Vector2 position, Vector2 direction, float size, Vector4 color)
    {
        var arrowColor = ImGui.ColorConvertFloat4ToU32(color);

        // Calculate arrow points
        var tip = position;
        var perpendicular = new Vector2(-direction.Y, direction.X);
        var base1 = tip - direction * size + perpendicular * (size * 0.5f);
        var base2 = tip - direction * size - perpendicular * (size * 0.5f);

        // Draw filled triangle
        drawList.AddTriangleFilled(tip, base1, base2, arrowColor);
        drawList.AddTriangle(tip, base1, base2, ImGui.ColorConvertFloat4ToU32(Vector4.One), 2);
    }

    private static void DrawDistanceText(ImDrawListPtr drawList, IPlayerCharacter localPlayer,
        IPlayerCharacter otherPlayer, Vector2 startPos, Vector2 endPos)
    {
        var distance = Vector3.Distance(localPlayer.Position, otherPlayer.Position);
        var distanceText = $"{distance:F1}y";
        var midPoint = new Vector2((startPos.X + endPos.X) / 2, (startPos.Y + endPos.Y) / 2);

        DrawTextWithBackground(drawList, distanceText, midPoint, Vector4.One, new Vector4(0, 0, 0, 0.7f));
    }

    private void DrawPlayerInfo(ImDrawListPtr drawList, IPlayerCharacter player, Vector2 screenPos, PlayerDisplayData data)
    {
        var textYOffset = -25f;

        if (config.ShowAllyEnemyIndicator)
        {
            textYOffset = DrawAllyEnemyIndicator(drawList, screenPos, textYOffset, data.IsAlly);
        }

        if (config.ShowLowHealthIndicator && data.IsLowHealth && !data.IsAlly)
        {
            textYOffset = DrawLowHealthIndicator(drawList, screenPos, textYOffset, data.HealthPercentage);
        }

        // Draw defensive buff indicators for enemies
        if (config.ShowDefensiveBuffs && !data.IsAlly)
        {
            if (data.IsGuarding)
            {
                textYOffset = DrawDefensiveBuffIndicator(drawList, screenPos, textYOffset, "GUARD - AVOID", new Vector4(0.0f, 0.0f, 1.0f, 1.0f));
            }
            else if (data.IsRecuperating)
            {
                textYOffset = DrawDefensiveBuffIndicator(drawList, screenPos, textYOffset, "HEALING", new Vector4(0.0f, 0.8f, 0.8f, 1.0f));
            }
            else if (data.HasDefensiveBuff)
            {
                textYOffset = DrawDefensiveBuffIndicator(drawList, screenPos, textYOffset, "DEFENSIVE", new Vector4(0.5f, 0.5f, 1.0f, 1.0f));
            }
        }

        if (config.ShowPlayerJobs)
        {
            textYOffset = DrawJobInfo(drawList, player, screenPos, textYOffset, data.Role);
        }

        if (config.ShowPlayerNames && player.Name?.TextValue != null)
        {
            textYOffset = DrawPlayerName(drawList, player, screenPos, textYOffset);
        }

        if (config.ShowStatusEffects && StatusEffectHelper.HasImportantStatusEffects(player))
        {
            var statusEffects = StatusEffectHelper.GetImportantStatusEffects(player, config.ShowOnlyImportantStatus);
            DrawStatusEffects(drawList, statusEffects, screenPos, textYOffset, data.IsCrowdControlled);
        }
    }

    private float DrawAllyEnemyIndicator(ImDrawListPtr drawList, Vector2 screenPos, float textYOffset, bool isAlly)
    {
        var statusText = isAlly ? "ALLY" : "ENEMY";
        var statusColor = isAlly ? config.AllyLineColor : new Vector4(1.0f, 0.0f, 0.0f, 1.0f);
        var bgColor = isAlly ? new Vector4(0.0f, 0.3f, 0.0f, 0.8f) : new Vector4(0.3f, 0.0f, 0.0f, 0.8f);

        DrawTextWithBackground(drawList, statusText,
            new Vector2(screenPos.X, screenPos.Y + textYOffset), statusColor, bgColor, 3);
        return textYOffset - 20f;
    }

    private float DrawLowHealthIndicator(ImDrawListPtr drawList, Vector2 screenPos, float textYOffset, float healthPercentage)
    {
        var healthText = config.ShowHealthPercentage ? $"{healthPercentage:F0}% HP" : "KILLABLE";
        var bgAlpha = config.PulseKillableTargets
            ? (float)(0.6f + 0.4f * Math.Sin(DateTime.Now.Millisecond * 0.01f))
            : 0.8f;
        var bgColor = new Vector4(0.8f, 0.0f, 0.0f, bgAlpha);

        DrawTextWithBackground(drawList, healthText,
            new Vector2(screenPos.X, screenPos.Y + textYOffset), config.LowHealthLineColor, bgColor, 3);
        return textYOffset - 20f;
    }

    private float DrawJobInfo(ImDrawListPtr drawList, IPlayerCharacter player, Vector2 screenPos, float textYOffset, PlayerRole role)
    {
        var jobText = config.ShowJobIcons ? JobHelper.GetJobAbbreviation(player) : JobHelper.GetRoleAbbreviation(role);
        var jobColor = config.ColorCodeByRole ? JobHelper.GetRoleColor(role) : Vector4.One;

        DrawTextWithBackground(drawList, jobText,
            new Vector2(screenPos.X, screenPos.Y + textYOffset), jobColor, new Vector4(0, 0, 0, 0.8f), 2);
        return textYOffset - 18f;
    }

    private static float DrawPlayerName(ImDrawListPtr drawList, IPlayerCharacter player, Vector2 screenPos, float textYOffset)
    {
        DrawTextWithBackground(drawList, player.Name.TextValue,
            new Vector2(screenPos.X, screenPos.Y + textYOffset), Vector4.One, new Vector4(0, 0, 0, 0.7f), 2);
        return textYOffset - 18f;
    }

    private static float DrawDefensiveBuffIndicator(ImDrawListPtr drawList, Vector2 screenPos, float textYOffset, string text, Vector4 textColor)
    {
        var bgColor = new Vector4(textColor.X * 0.3f, textColor.Y * 0.3f, textColor.Z * 0.3f, 0.9f);

        // Add pulsing effect for Guard to make it extra obvious
        if (text.Contains("GUARD"))
        {
            var pulseIntensity = (float)(0.8f + 0.2f * Math.Sin(DateTime.Now.Millisecond * 0.015f));
            textColor = new Vector4(textColor.X, textColor.Y, textColor.Z, pulseIntensity);
            bgColor = new Vector4(bgColor.X, bgColor.Y, bgColor.Z, bgColor.W * pulseIntensity);
        }

        DrawTextWithBackground(drawList, text,
            new Vector2(screenPos.X, screenPos.Y + textYOffset), textColor, bgColor, 4);
        return textYOffset - 22f;
    }

    private static void DrawTextWithBackground(ImDrawListPtr drawList, string text, Vector2 centerPos,
        Vector4 textColor, Vector4 bgColor, int padding = 2)
    {
        var textSize = ImGui.CalcTextSize(text);
        var textPos = new Vector2(centerPos.X - textSize.X / 2, centerPos.Y - textSize.Y / 2);
        var bgStart = new Vector2(textPos.X - padding, textPos.Y - padding);
        var bgEnd = new Vector2(textPos.X + textSize.X + padding, textPos.Y + textSize.Y + padding);

        drawList.AddRectFilled(bgStart, bgEnd, ImGui.ColorConvertFloat4ToU32(bgColor));
        drawList.AddText(textPos, ImGui.ColorConvertFloat4ToU32(textColor), text);
    }

    private static void DrawStatusEffects(ImDrawListPtr drawList, List<StatusInfo> statusEffects,
        Vector2 screenPos, float startYOffset, bool isCrowdControlled)
    {
        if (statusEffects.Count == 0) return;

        var textYOffset = startYOffset - 20f;
        var maxEffectsToShow = 3; // Limit to prevent clutter
        var effectsShown = 0;

        // Prioritize crowd control and vulnerability effects
        var prioritizedEffects = new List<StatusInfo>();
        var otherEffects = new List<StatusInfo>();

        foreach (var effect in statusEffects)
        {
            if (effect.Name.Contains("Stun") || effect.Name.Contains("Sleep") ||
                effect.Name.Contains("Silence") || effect.Name.Contains("Vulnerability"))
            {
                prioritizedEffects.Add(effect);
            }
            else
            {
                otherEffects.Add(effect);
            }
        }

        // Draw prioritized effects first
        foreach (var effect in prioritizedEffects)
        {
            if (effectsShown >= maxEffectsToShow) break;

            var displayText = effect.RemainingTime.HasValue
                ? $"{effect.Name} ({effect.RemainingTime.Value:F0}s)"
                : effect.Name;

            var bgColor = effect.IsNegative
                ? new Vector4(0.3f, 0.0f, 0.0f, 0.8f)
                : new Vector4(0.0f, 0.3f, 0.0f, 0.8f);

            // Add pulsing effect for crowd control
            if (isCrowdControlled && effect.IsNegative)
            {
                var pulseIntensity = (float)(0.7f + 0.3f * Math.Sin(DateTime.Now.Millisecond * 0.02f));
                bgColor = new Vector4(bgColor.X, bgColor.Y, bgColor.Z, bgColor.W * pulseIntensity);
            }

            DrawTextWithBackground(drawList, displayText,
                new Vector2(screenPos.X, screenPos.Y + textYOffset), effect.Color, bgColor, 2);

            textYOffset -= 16f;
            effectsShown++;
        }

        // Draw other effects if space remains
        foreach (var effect in otherEffects)
        {
            if (effectsShown >= maxEffectsToShow) break;

            var displayText = effect.RemainingTime.HasValue
                ? $"{effect.Name} ({effect.RemainingTime.Value:F0}s)"
                : effect.Name;

            var bgColor = effect.IsNegative
                ? new Vector4(0.3f, 0.0f, 0.0f, 0.8f)
                : new Vector4(0.0f, 0.3f, 0.0f, 0.8f);

            DrawTextWithBackground(drawList, displayText,
                new Vector2(screenPos.X, screenPos.Y + textYOffset), effect.Color, bgColor, 2);

            textYOffset -= 16f;
            effectsShown++;
        }
    }

    private static bool IsPvPTerritory(uint territoryType)
    {
        return PvPTerritoryIds.Contains(territoryType);
    }



    private bool TryGetScreenPosition(Vector3 worldPosition, out Vector2 screenPosition)
    {
        return Plugin.GameGui.WorldToScreen(worldPosition, out screenPosition);
    }

    // New enhanced indicator methods
    private void DrawWorldCircleIndicator(Vector2 startPos, Vector2 endPos, PlayerDisplayData data)
    {
        if (!config.Use3DWorldSpace)
        {
            DrawLineIndicator(startPos, endPos, data); // Fallback to lines
            return;
        }

        var drawList = PictoService.Draw();
        if (drawList == null) return;

        var (lineColor, _) = GetLineAppearance(data);
        var color = ColorUtils.ToImGuiColor(lineColor);

        // Get world position from screen position (simplified)
        var localPlayer = Plugin.ClientState.LocalPlayer;
        if (localPlayer == null) return;

        // For now, use a simple approximation - in a real implementation you'd reverse the world-to-screen projection
        var worldPos = localPlayer.Position; // This would be the actual enemy position

        drawList.AddCircle(worldPos, config.GroundCircleRadius, color, 2.0f);

        if (config.ShowGroundCircles)
        {
            drawList.AddCircleFilled(worldPos, config.GroundCircleRadius * 0.8f, ColorUtils.WithAlpha(color, 0.3f));
        }
    }

    private void DrawVfxOmenIndicator(Vector2 startPos, Vector2 endPos, PlayerDisplayData data)
    {
        if (!config.EnableVfxEffects)
        {
            DrawLineIndicator(startPos, endPos, data); // Fallback to lines
            return;
        }

        var vfxRenderer = PictoService.VfxRenderer;
        if (vfxRenderer == null) return;

        var localPlayer = Plugin.ClientState.LocalPlayer;
        if (localPlayer == null) return;

        var (lineColor, _) = GetLineAppearance(data);
        var color = ColorUtils.ToImGuiColor(lineColor);

        // Create unique ID for this enemy
        var enemyId = $"omen_{data.GetHashCode()}";

        // Add omen effect at enemy position
        vfxRenderer.AddOmen(enemyId, "general01bf", localPlayer.Position, config.GroundCircleRadius, color);
    }

    private void DrawVfxTetherIndicator(Vector2 startPos, Vector2 endPos, PlayerDisplayData data)
    {
        if (!config.EnableTethers)
        {
            DrawLineIndicator(startPos, endPos, data); // Fallback to lines
            return;
        }

        var vfxRenderer = PictoService.VfxRenderer;
        if (vfxRenderer == null) return;

        var localPlayer = Plugin.ClientState.LocalPlayer;
        if (localPlayer == null) return;

        // For tethers, we need both source and target objects
        // This is a simplified implementation - you'd need to get the actual enemy object
        var enemyId = $"tether_{data.GetHashCode()}";

        // In a real implementation, you'd pass the actual enemy IGameObject
        // vfxRenderer.AddChanneling(enemyId, config.TetherVfxPath, localPlayer, enemyObject);

        // For now, fallback to enhanced line
        DrawGradientLineIndicator(startPos, endPos, data);
    }

    private void DrawGroundMarkerIndicator(Vector2 startPos, Vector2 endPos, PlayerDisplayData data)
    {
        var drawList = ImGui.GetBackgroundDrawList();
        var (lineColor, _) = GetLineAppearance(data);

        if (config.UseDistanceBasedColors)
        {
            var localPlayer = Plugin.ClientState.LocalPlayer;
            if (localPlayer != null)
            {
                // Calculate distance-based color intensity
                var distance = Vector2.Distance(startPos, endPos);
                var maxScreenDistance = 500.0f; // Approximate max screen distance
                lineColor = ColorUtils.GetDistanceColor(distance, maxScreenDistance, lineColor);
            }
        }

        var color = ColorUtils.ToImGuiColor(lineColor);
        var markerSize = config.GroundCircleRadius * 10; // Convert to screen space

        // Draw ground marker as a filled circle with border
        drawList.AddCircleFilled(endPos, markerSize, ColorUtils.WithAlpha(color, 0.6f));
        drawList.AddCircle(endPos, markerSize, color, 0, 2.0f);

        // Add a cross in the center
        var crossSize = markerSize * 0.5f;
        drawList.AddLine(
            new Vector2(endPos.X - crossSize, endPos.Y),
            new Vector2(endPos.X + crossSize, endPos.Y),
            color, 2.0f);
        drawList.AddLine(
            new Vector2(endPos.X, endPos.Y - crossSize),
            new Vector2(endPos.X, endPos.Y + crossSize),
            color, 2.0f);
    }

    private void DrawFloatingIconIndicator(Vector2 startPos, Vector2 endPos, PlayerDisplayData data)
    {
        var drawList = ImGui.GetBackgroundDrawList();
        var (lineColor, _) = GetLineAppearance(data);

        if (config.UseHealthBasedColors && !data.IsAlly)
        {
            lineColor = ColorUtils.GetHealthColor(data.HealthPercentage / 100.0f);
        }

        var color = ColorUtils.ToImGuiColor(lineColor);
        var iconSize = config.IconSize;

        // Draw floating icon above enemy position
        var iconPos = new Vector2(endPos.X, endPos.Y - 30);

        // Draw role-based icon background
        var roleColor = ColorUtils.GetRoleColor((uint)data.Role);
        drawList.AddCircleFilled(iconPos, iconSize / 2, ColorUtils.ToImGuiColor(roleColor));
        drawList.AddCircle(iconPos, iconSize / 2, color, 0, 2.0f);

        // Add role letter
        var roleChar = data.Role.ToString()[0].ToString();
        var textSize = ImGui.CalcTextSize(roleChar);
        var textPos = new Vector2(iconPos.X - textSize.X / 2, iconPos.Y - textSize.Y / 2);
        drawList.AddText(textPos, ColorUtils.ToImGuiColor(ColorUtils.Colors.White), roleChar);

        // Add health indicator if enabled
        if (config.UseHealthBasedColors && !data.IsAlly)
        {
            var healthBarPos = new Vector2(iconPos.X - iconSize / 2, iconPos.Y + iconSize / 2 + 5);
            var healthBarSize = new Vector2(iconSize, 4);
            var healthWidth = healthBarSize.X * (data.HealthPercentage / 100.0f);

            // Background
            drawList.AddRectFilled(healthBarPos, healthBarPos + healthBarSize, ColorUtils.ToImGuiColor(ColorUtils.Colors.DarkGray));

            // Health fill
            var healthColor = ColorUtils.GetHealthColor(data.HealthPercentage / 100.0f);
            drawList.AddRectFilled(healthBarPos, new Vector2(healthBarPos.X + healthWidth, healthBarPos.Y + healthBarSize.Y),
                ColorUtils.ToImGuiColor(healthColor));
        }
    }

    private void DrawScreenEdgeArrowIndicator(Vector2 startPos, Vector2 endPos, PlayerDisplayData data)
    {
        var drawList = ImGui.GetBackgroundDrawList();
        var (lineColor, _) = GetLineAppearance(data);
        var color = ColorUtils.ToImGuiColor(lineColor);

        // Check if enemy is off-screen
        var screenSize = ImGui.GetIO().DisplaySize;
        var margin = config.ScreenEdgeMargin;

        if (endPos.X >= margin && endPos.X <= screenSize.X - margin &&
            endPos.Y >= margin && endPos.Y <= screenSize.Y - margin)
        {
            // Enemy is on-screen, draw normal indicator
            DrawIconIndicator(endPos, data);
            return;
        }

        // Enemy is off-screen, draw edge arrow
        var direction = Vector2.Normalize(endPos - startPos);
        drawList.AddScreenEdgeArrow(startPos, direction, color, config.ArrowSize, margin);
    }

    private void DrawGradientLineIndicator(Vector2 startPos, Vector2 endPos, PlayerDisplayData data)
    {
        var drawList = ImGui.GetBackgroundDrawList();
        var (startColor, lineThickness) = GetLineAppearance(data);
        var endColor = config.GradientEndColor;

        if (config.UseDistanceBasedColors)
        {
            var distance = Vector2.Distance(startPos, endPos);
            var maxDistance = 500.0f; // Max screen distance
            endColor = ColorUtils.GetDistanceColor(distance, maxDistance, endColor);
        }

        drawList.AddGradientLine(startPos, endPos, ColorUtils.ToImGuiColor(startColor), ColorUtils.ToImGuiColor(endColor), lineThickness);
    }

    private void DrawAnimatedPulseIndicator(Vector2 startPos, Vector2 endPos, PlayerDisplayData data)
    {
        var drawList = ImGui.GetBackgroundDrawList();
        var (baseColor, lineThickness) = GetLineAppearance(data);

        if (config.UseHealthBasedColors && !data.IsAlly)
        {
            baseColor = ColorUtils.GetHealthColor(data.HealthPercentage / 100.0f);
        }

        // Check if we should animate based on conditions
        var shouldAnimate = config.AnimateOnLowHealth && data.IsLowHealth ||
                           config.AnimateOnDefensiveBuff && data.HasDefensiveBuff ||
                           !config.AnimateOnLowHealth && !config.AnimateOnDefensiveBuff;

        if (shouldAnimate)
        {
            // Draw pulsing circle at enemy position
            drawList.AddPulsingCircleFilled(endPos, config.IconSize / 2, ColorUtils.ToImGuiColor(baseColor),
                config.PulseSpeed, config.PulseAmount);

            // Draw pulsing line
            var time = (float)ImGui.GetTime();
            var pulse = (float)(Math.Sin(time * config.PulseSpeed) * config.PulseAmount + 1.0f);
            var pulsedThickness = lineThickness * pulse;
            var pulsedColor = ColorUtils.WithAlpha(baseColor, baseColor.W * pulse);

            drawList.AddLine(startPos, endPos, ColorUtils.ToImGuiColor(pulsedColor), pulsedThickness);
        }
        else
        {
            // Draw normal line when not animating
            DrawLineIndicator(startPos, endPos, data);
        }
    }
}
