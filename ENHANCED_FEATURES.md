# Enhanced PvP Line Plugin Features

## Overview
The PvP Line Plugin has been significantly enhanced with new 3D world indicators, advanced 2D rendering, and VFX integration. These improvements provide more immersive and informative visual indicators for PvP combat.

## New Indicator Types

### 🌐 3D World Indicators
- **World Circles**: 3D circles around enemies visible from any camera angle
- **VFX Omens**: Game-native omen effects using the VFX system
- **VFX Tethers**: Beam effects connecting you to enemies
- **Ground Markers**: Markers on the ground at enemy positions

### ✨ Enhanced 2D Indicators  
- **Gradient Lines**: Lines with smooth color transitions
- **Animated Pulse**: Pulsing indicators with customizable animation
- **Screen Edge Arrows**: Arrows pointing to off-screen enemies
- **Floating Icons**: 3D icons above enemies with job and health info

## Advanced Features

### 🎨 Dynamic Coloring
- **Distance-Based**: Indicators become more intense as enemies get closer
- **Health-Based**: Colors change based on enemy health percentage
- **Role-Based**: Automatic coloring based on job roles (Tank/Healer/DPS)

### 🎬 Smart Animations
- **Conditional Animation**: Animate only when enemies have low health or defensive buffs
- **Customizable Speed**: Adjust pulse speed and intensity
- **Performance Optimized**: Minimal impact on frame rate

### 🎯 Enhanced Targeting
- **Off-Screen Tracking**: Arrows at screen edges for enemies outside view
- **Obstacle Penetration**: Ground markers visible through terrain
- **Multi-Layer Information**: Combine multiple indicator types

## Technical Improvements

### 🔧 New Libraries Integrated
- **Pictomancy**: 3D world-space rendering and VFX integration
- **Enhanced ImGui Utils**: Advanced drawing functions (gradients, dashed lines, animations)
- **Color Utilities**: Comprehensive color management and role-based coloring

### ⚡ Performance Optimizations
- **Object Pooling**: Reduced memory allocations
- **Conditional Rendering**: Only draw what's needed
- **Efficient Animations**: Optimized pulse and gradient calculations

### 🛠️ Configuration System
- **Expanded Settings**: 20+ new configuration options
- **Type-Specific Options**: Each indicator type has its own settings panel
- **Real-Time Preview**: See changes immediately

## Usage Examples

### For Large PvP Battles (Frontlines)
```
Recommended: Screen Edge Arrows + Distance-Based Colors
- Tracks off-screen enemies
- Prioritizes closer threats
- Minimal performance impact
```

### For Small PvP Battles (Crystalline Conflict)
```
Recommended: VFX Omens + Animated Pulse + Health-Based Colors
- Immersive visual effects
- Clear health status indication
- Enhanced situational awareness
```

### For Competitive PvP
```
Recommended: Gradient Lines + Floating Icons + Role-Based Colors
- Clean, informative display
- Quick role identification
- Professional appearance
```

## Configuration Guide

### Accessing New Features
1. Open plugin configuration (`/pvpline config`)
2. Go to "Appearance" tab
3. Select new indicator type from dropdown
4. Configure type-specific settings below

### Key Settings

#### 3D World Space
- **Enable 3D World Space Rendering**: Activates 3D indicators
- **Circle Radius**: Size of world circles (0.5-10.0)
- **VFX Opacity**: Transparency of VFX effects (0.1-1.0)

#### Enhanced 2D
- **Use Gradient Lines**: Enable color gradients on lines
- **Gradient End Color**: Second color for gradients
- **Use Dashed Lines**: Create dashed line patterns
- **Screen Edge Margin**: Distance from edge for arrows

#### Animation
- **Pulse Speed**: How fast indicators pulse (0.5-5.0)
- **Pulse Amount**: Intensity of pulsing (0.1-1.0)
- **Animate on Low Health**: Only animate low-health enemies
- **Health-Based Colors**: Dynamic coloring by health

## Compatibility

### Game Versions
- ✅ FFXIV 6.5+
- ✅ Dalamud API 9+
- ✅ All PvP zones and modes

### Performance Requirements
- **Minimum**: Standard 2D indicators work on all systems
- **Recommended**: Dedicated GPU for 3D indicators and VFX
- **High-End**: All features enabled for maximum visual fidelity

### Known Limitations
- VFX effects may not work in some instanced content
- 3D indicators require world-to-screen projection (simplified implementation)
- Some VFX paths may be zone-specific

## Migration from Previous Version

### Automatic Migration
- All existing settings are preserved
- New features are disabled by default
- No configuration changes required

### Recommended Upgrades
1. Try **Gradient Lines** as a drop-in replacement for basic lines
2. Enable **Screen Edge Arrows** for better situational awareness  
3. Experiment with **Health-Based Colors** for target prioritization

## Troubleshooting

### Common Issues
- **3D indicators not showing**: Enable "3D World Space Rendering"
- **VFX effects missing**: Check VFX paths and zone compatibility
- **Performance drops**: Disable animations and 3D features
- **Colors not changing**: Verify color-based features are enabled

### Performance Optimization
1. Disable VFX effects in large battles
2. Reduce animation speed and intensity
3. Use simpler indicator types for better frame rates
4. Enable only essential features for competitive play

## Future Enhancements

### Planned Features
- **Minimap Integration**: Show indicators on minimap
- **Audio Cues**: Sound alerts for specific conditions
- **Custom VFX**: User-defined VFX effects
- **Advanced Filtering**: More granular enemy filtering options

### Community Feedback
We welcome feedback on the new features! Please report issues or suggestions through the plugin's GitHub repository.

---

*Enhanced PvP Line Plugin - Bringing next-generation visual indicators to FFXIV PvP*
