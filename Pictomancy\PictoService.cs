using System;
using Dalamud.Plugin;
using Dalamud.Logging;

namespace Pictomancy
{
    public static class PictoService
    {
        private static DalamudPluginInterface? _pluginInterface;
        private static ImGuiRenderer? _imguiRenderer;
        private static VfxRenderer? _vfxRenderer;
        private static bool _initialized = false;

        public static ImGuiRenderer? ImGuiRenderer => _imguiRenderer;
        public static VfxRenderer? VfxRenderer => _vfxRenderer;

        public static void Initialize(DalamudPluginInterface pluginInterface)
        {
            if (_initialized)
                return;

            _pluginInterface = pluginInterface;
            
            try
            {
                _imguiRenderer = new ImGuiRenderer();
                _vfxRenderer = new VfxRenderer();
                _initialized = true;
                
                PluginLog.Information("Pictomancy service initialized successfully");
            }
            catch (Exception ex)
            {
                PluginLog.Error($"Failed to initialize Pictomancy service: {ex.Message}");
            }
        }

        public static PctDrawList? Draw()
        {
            if (!_initialized || _imguiRenderer == null)
                return null;

            return _imguiRenderer.GetDrawList();
        }

        public static void Dispose()
        {
            if (!_initialized)
                return;

            try
            {
                _vfxRenderer?.Dispose();
                _imguiRenderer?.Dispose();
                _initialized = false;
                
                PluginLog.Information("Pictomancy service disposed");
            }
            catch (Exception ex)
            {
                PluginLog.Error($"Error disposing Pictomancy service: {ex.Message}");
            }
        }
    }
}
