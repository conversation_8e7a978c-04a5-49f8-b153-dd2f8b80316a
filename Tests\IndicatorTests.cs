using System;
using System.Numerics;
using PvPLinePlugin.Utils;
using Xunit;

namespace PvPLinePlugin.Tests
{
    public class IndicatorTests
    {
        [Fact]
        public void ColorUtils_ToImGuiColor_ConvertsCorrectly()
        {
            // Arrange
            var color = new Vector4(1.0f, 0.5f, 0.0f, 1.0f); // Orange
            
            // Act
            var result = ColorUtils.ToImGuiColor(color);
            
            // Assert
            Assert.NotEqual(0u, result);
        }

        [Fact]
        public void ColorUtils_WithAlpha_ModifiesAlphaCorrectly()
        {
            // Arrange
            var originalColor = new Vector4(1.0f, 0.0f, 0.0f, 1.0f); // Red
            var newAlpha = 0.5f;
            
            // Act
            var result = ColorUtils.WithAlpha(originalColor, newAlpha);
            
            // Assert
            Assert.Equal(newAlpha, result.W);
            Assert.Equal(originalColor.X, result.X);
            Assert.Equal(originalColor.Y, result.Y);
            Assert.Equal(originalColor.Z, result.Z);
        }

        [Fact]
        public void ColorUtils_GetRoleColor_ReturnsValidColors()
        {
            // Test tank job (Paladin = 19)
            var tankColor = ColorUtils.GetRoleColor(19);
            Assert.Equal(new Vector4(0.0f, 0.6f, 1.0f, 1.0f), tankColor);

            // Test healer job (White Mage = 24)
            var healerColor = ColorUtils.GetRoleColor(24);
            Assert.Equal(new Vector4(0.0f, 1.0f, 0.3f, 1.0f), healerColor);

            // Test DPS job (Black Mage = 25)
            var dpsColor = ColorUtils.GetRoleColor(25);
            Assert.Equal(new Vector4(0.8f, 0.0f, 1.0f, 1.0f), dpsColor);
        }

        [Fact]
        public void ColorUtils_GetHealthColor_ReturnsCorrectGradient()
        {
            // Test full health (green)
            var fullHealth = ColorUtils.GetHealthColor(1.0f);
            Assert.Equal(new Vector4(0.0f, 1.0f, 0.0f, 1.0f), fullHealth);

            // Test low health (red)
            var lowHealth = ColorUtils.GetHealthColor(0.1f);
            Assert.True(lowHealth.X > 0.8f); // Should be mostly red

            // Test medium health (yellow/orange)
            var mediumHealth = ColorUtils.GetHealthColor(0.5f);
            Assert.True(mediumHealth.X > 0.5f && mediumHealth.Y > 0.5f); // Should have red and green components
        }

        [Fact]
        public void ColorUtils_GetDistanceColor_ModifiesIntensity()
        {
            // Arrange
            var baseColor = new Vector4(1.0f, 0.0f, 0.0f, 1.0f); // Red
            var distance = 250.0f;
            var maxDistance = 500.0f;
            
            // Act
            var result = ColorUtils.GetDistanceColor(distance, maxDistance, baseColor);
            
            // Assert
            // At half distance, intensity should be 0.5, so alpha should be between base alpha and 0.3
            Assert.True(result.W < baseColor.W);
            Assert.True(result.W > 0.3f);
        }

        [Theory]
        [InlineData(IndicatorType.Lines)]
        [InlineData(IndicatorType.WorldCircles)]
        [InlineData(IndicatorType.VfxOmens)]
        [InlineData(IndicatorType.GradientLines)]
        [InlineData(IndicatorType.AnimatedPulse)]
        public void Configuration_SupportsAllIndicatorTypes(IndicatorType indicatorType)
        {
            // Arrange
            var config = new Configuration();
            
            // Act
            config.IndicatorType = indicatorType;
            
            // Assert
            Assert.Equal(indicatorType, config.IndicatorType);
        }

        [Fact]
        public void Configuration_NewPropertiesHaveDefaults()
        {
            // Arrange & Act
            var config = new Configuration();
            
            // Assert - Check that new properties have sensible defaults
            Assert.False(config.Use3DWorldSpace);
            Assert.False(config.EnableVfxEffects);
            Assert.Equal(0.8f, config.VfxOpacity);
            Assert.True(config.ShowGroundCircles);
            Assert.Equal(2.0f, config.GroundCircleRadius);
            Assert.False(config.EnableTethers);
            Assert.Equal("chn_nomal01f", config.TetherVfxPath);
            
            Assert.False(config.UseGradientLines);
            Assert.False(config.UseDashedLines);
            Assert.Equal(5.0f, config.DashLength);
            Assert.Equal(3.0f, config.GapLength);
            Assert.True(config.ShowScreenEdgeArrows);
            Assert.Equal(50.0f, config.ScreenEdgeMargin);
            
            Assert.Equal(2.0f, config.PulseSpeed);
            Assert.Equal(0.3f, config.PulseAmount);
            Assert.True(config.AnimateOnLowHealth);
            Assert.False(config.AnimateOnDefensiveBuff);
        }
    }
}
