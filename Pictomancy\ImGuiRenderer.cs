using System;
using Dalamud.Logging;

namespace Pictomancy
{
    public class ImGuiRenderer : IDisposable
    {
        private bool _disposed = false;
        private PctDrawList? _currentDrawList;

        public PctDrawList? GetDrawList()
        {
            if (_disposed) return null;
            
            try
            {
                _currentDrawList?.Dispose();
                _currentDrawList = new PctDrawList();
                return _currentDrawList;
            }
            catch (Exception ex)
            {
                PluginLog.Error($"Error creating draw list: {ex.Message}");
                return null;
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _currentDrawList?.Dispose();
                _disposed = true;
            }
        }
    }
}
